# Firebase ignore file - Optimized for smaller upload
node_modules/
.git/
.gitignore
.firebaserc
firebase.json
*.log
.DS_Store
Thumbs.db
*.tmp
*.temp
.vscode/
.idea/
*.md
README*
LICENSE*
package*.json
yarn.lock
.env*

# EXCLUDE LARGE ELECTRON DIST FILES
dist/
build/
coverage/
.nyc_output/
*.test.js
*.spec.js
test/
tests/
__tests__/

# Exclude large binary files
*.dmg
*.dmg.blockmap
*.app
*.exe
*.deb
*.rpm
*.zip
*.tar.gz

# Exclude screenshots and large assets
screenshots/
*.mov
*.mp4
*.avi
