[debug] [2025-07-03T17:13:44.973Z] ----------------------------------------------------------------------
[debug] [2025-07-03T17:13:44.975Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/augment-projects/Generator/node_modules/.bin/firebase init hosting --project ehroby-e3388
[debug] [2025-07-03T17:13:44.975Z] CLI Version:   14.9.0
[debug] [2025-07-03T17:13:44.975Z] Platform:      darwin
[debug] [2025-07-03T17:13:44.975Z] Node Version:  v22.16.0
[debug] [2025-07-03T17:13:44.975Z] Time:          Thu Jul 03 2025 19:13:44 GMT+0200 (Central European Summer Time)
[debug] [2025-07-03T17:13:44.975Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-03T17:13:45.079Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-03T17:13:45.080Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Documents/augment-projects/Generator

Before we get started, keep in mind:

  * You are initializing within an existing Firebase project directory

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-07-03T17:13:45.081Z] Using project from CLI flag: ehroby-e3388
[debug] [2025-07-03T17:13:45.081Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T17:13:45.081Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T17:13:45.082Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 [none]
[debug] [2025-07-03T17:13:45.111Z] *** [apiv2] error from fetch(https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388, {"headers":{},"method":"GET","signal":{}}): FetchError: request to https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 failed, reason: getaddrinfo ENOTFOUND firebase.googleapis.com
[debug] [2025-07-03T17:13:45.111Z] Failed to make request to https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 (original: request to https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 failed, reason: getaddrinfo ENOTFOUND firebase.googleapis.com)
[debug] [2025-07-03T17:13:45.114Z] FirebaseError: Failed to make request to https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388
    at RetryOperation._fn (/Users/<USER>/Documents/augment-projects/Generator/node_modules/firebase-tools/lib/apiv2.js:261:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[error] 
[error] Error: Failed to get Firebase project ehroby-e3388. Please make sure the project exists and your account has permission to access it.
[debug] [2025-07-03T17:13:54.530Z] ----------------------------------------------------------------------
[debug] [2025-07-03T17:13:54.532Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/augment-projects/Generator/node_modules/.bin/firebase projects:list
[debug] [2025-07-03T17:13:54.532Z] CLI Version:   14.9.0
[debug] [2025-07-03T17:13:54.532Z] Platform:      darwin
[debug] [2025-07-03T17:13:54.532Z] Node Version:  v22.16.0
[debug] [2025-07-03T17:13:54.532Z] Time:          Thu Jul 03 2025 19:13:54 GMT+0200 (Central European Summer Time)
[debug] [2025-07-03T17:13:54.532Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-03T17:13:54.632Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-03T17:13:54.632Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-03T17:13:54.633Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T17:13:54.634Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T17:13:54.634Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-03T17:13:54.660Z] *** [apiv2] error from fetch(https://firebase.googleapis.com/v1beta1/projects?pageSize=1000, {"headers":{},"method":"GET","signal":{}}): FetchError: request to https://firebase.googleapis.com/v1beta1/projects?pageSize=1000 failed, reason: getaddrinfo ENOTFOUND firebase.googleapis.com
[debug] [2025-07-03T17:13:54.660Z] Failed to make request to https://firebase.googleapis.com/v1beta1/projects?pageSize=1000
[debug] [2025-07-03T17:13:54.665Z] FirebaseError: Failed to make request to https://firebase.googleapis.com/v1beta1/projects?pageSize=1000
    at RetryOperation._fn (/Users/<USER>/Documents/augment-projects/Generator/node_modules/firebase-tools/lib/apiv2.js:261:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[error] 
[error] Error: Failed to list Firebase projects. See firebase-debug.log for more info.
