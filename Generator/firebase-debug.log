[debug] [2025-07-03T16:46:32.634Z] ----------------------------------------------------------------------
[debug] [2025-07-03T16:46:32.642Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/augment-projects/Generator/node_modules/.bin/firebase deploy --only hosting
[debug] [2025-07-03T16:46:32.642Z] CLI Version:   14.9.0
[debug] [2025-07-03T16:46:32.643Z] Platform:      darwin
[debug] [2025-07-03T16:46:32.643Z] Node Version:  v22.16.0
[debug] [2025-07-03T16:46:32.643Z] Time:          Thu Jul 03 2025 18:46:32 GMT+0200 (Central European Summer Time)
[debug] [2025-07-03T16:46:32.643Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-03T16:46:32.862Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-03T16:46:32.862Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-03T16:46:32.862Z] [iam] checking project ehroby-e3388 for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-07-03T16:46:32.863Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:32.863Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:32.863Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/ehroby-e3388:testIamPermissions [none]
[debug] [2025-07-03T16:46:32.863Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/ehroby-e3388:testIamPermissions x-goog-quota-user=projects/ehroby-e3388
[debug] [2025-07-03T16:46:32.863Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/ehroby-e3388:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-03T16:46:33.728Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/ehroby-e3388:testIamPermissions 200
[debug] [2025-07-03T16:46:33.728Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/ehroby-e3388:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-03T16:46:33.728Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:33.728Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:33.728Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 [none]
[debug] [2025-07-03T16:46:34.131Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 200
[debug] [2025-07-03T16:46:34.131Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/ehroby-e3388 {"projectId":"ehroby-e3388","projectNumber":"275727532271","displayName":"eHroby","name":"projects/ehroby-e3388","resources":{"hostingSite":"ehroby-e3388"},"state":"ACTIVE","etag":"1_072d4a8d-b915-4df5-b439-4fe6eb4bcbc6"}
[info] 
[info] === Deploying to 'ehroby-e3388'...
[info] 
[info] i  deploying hosting 
[debug] [2025-07-03T16:46:34.136Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:34.136Z] Checked if tokens are valid: true, expires at: 1751564457695
[debug] [2025-07-03T16:46:34.136Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/ehroby-e3388/versions [none]
[debug] [2025-07-03T16:46:34.136Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/ehroby-e3388/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase"}}
[debug] [2025-07-03T16:46:35.922Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/ehroby-e3388/versions 200
[debug] [2025-07-03T16:46:35.922Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/ehroby-e3388/versions {"name":"projects/275727532271/sites/ehroby-e3388/versions/9023df146bb3bae2","status":"CREATED","config":{},"labels":{"deployment-tool":"cli-firebase"}}
[info] i  hosting[ehroby-e3388]: beginning deploy... 
[info] i  hosting[ehroby-e3388]: found 560 files in . 
[debug] [2025-07-03T16:46:35.994Z] [hosting] uploading with 200 concurrency
